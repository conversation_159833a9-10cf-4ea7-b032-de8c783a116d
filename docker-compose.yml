version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:14
    container_name: testgenius_postgres
    environment:
      POSTGRES_DB: testgenius
      POSTGRES_USER: testgenius
      POSTGRES_PASSWORD: testgenius123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - testgenius_network

  # Redis缓存和消息队列
  redis:
    image: redis:6-alpine
    container_name: testgenius_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - testgenius_network

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: testgenius_backend
    environment:
      - DATABASE_URL=***************************************************/testgenius
      - REDIS_URL=redis://redis:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - test_artifacts:/app/artifacts
    networks:
      - testgenius_network
    restart: unless-stopped

  # Celery Worker (异步任务处理)
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: testgenius_celery_worker
    command: celery -A core.celery worker --loglevel=info
    environment:
      - DATABASE_URL=***************************************************/testgenius
      - REDIS_URL=redis://redis:6379/0
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - test_artifacts:/app/artifacts
    networks:
      - testgenius_network
    restart: unless-stopped

  # Celery Beat (定时任务调度)
  celery_beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: testgenius_celery_beat
    command: celery -A core.celery beat --loglevel=info
    environment:
      - DATABASE_URL=***************************************************/testgenius
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
    networks:
      - testgenius_network
    restart: unless-stopped

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: testgenius_frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - testgenius_network
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: testgenius_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - testgenius_network
    restart: unless-stopped

  # Elasticsearch (日志聚合)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: testgenius_elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - testgenius_network

  # Kibana (日志可视化)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: testgenius_kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - testgenius_network

  # Prometheus (监控)
  prometheus:
    image: prom/prometheus:latest
    container_name: testgenius_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - testgenius_network

  # Grafana (监控可视化)
  grafana:
    image: grafana/grafana:latest
    container_name: testgenius_grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - testgenius_network

  # MinIO (对象存储)
  minio:
    image: minio/minio:latest
    container_name: testgenius_minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=testgenius
      - MINIO_ROOT_PASSWORD=testgenius123
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - testgenius_network

  # Selenium Grid Hub (测试执行环境)
  selenium_hub:
    image: selenium/hub:4.15.0
    container_name: testgenius_selenium_hub
    ports:
      - "4444:4444"
    environment:
      - GRID_MAX_SESSION=16
      - GRID_BROWSER_TIMEOUT=300
      - GRID_TIMEOUT=300
    networks:
      - testgenius_network

  # Chrome节点
  selenium_chrome:
    image: selenium/node-chrome:4.15.0
    container_name: testgenius_selenium_chrome
    environment:
      - HUB_HOST=selenium_hub
      - HUB_PORT=4444
      - NODE_MAX_INSTANCES=4
      - NODE_MAX_SESSION=4
    depends_on:
      - selenium_hub
    networks:
      - testgenius_network
    scale: 2

  # Firefox节点
  selenium_firefox:
    image: selenium/node-firefox:4.15.0
    container_name: testgenius_selenium_firefox
    environment:
      - HUB_HOST=selenium_hub
      - HUB_PORT=4444
      - NODE_MAX_INSTANCES=4
      - NODE_MAX_SESSION=4
    depends_on:
      - selenium_hub
    networks:
      - testgenius_network

volumes:
  postgres_data:
  redis_data:
  elasticsearch_data:
  prometheus_data:
  grafana_data:
  minio_data:
  test_artifacts:

networks:
  testgenius_network:
    driver: bridge
