import { useQuery } from 'react-query'
import { dashboardApi } from '@/api/dashboard'

export interface DashboardOverview {
  activeProjects: number
  runningTests: number
  todayDefects: number
  fixedDefects: number
  coverageRate: number
  projectHealthScore: number
  estimatedCompletion: string
  teamMembers: number
  weeklyTests: number
  successRate: number
}

export interface ProjectHealth {
  id: string
  name: string
  status: 'healthy' | 'warning' | 'critical'
  healthScore: number
  lastTestRun: string
  coverageRate: number
  defectCount: number
  trend: 'up' | 'down' | 'stable'
}

export interface RecentTest {
  id: string
  name: string
  project: string
  status: 'running' | 'passed' | 'failed' | 'pending'
  startTime: string
  duration: number
  executor: string
  progress?: number
}

export interface AgentStatus {
  orchestratorReady: boolean
  totalAgents: number
  activeSessions: number
  agents: {
    [key: string]: {
      name: string
      type: string
      ready: boolean
      lastActivity: string | null
    }
  }
}

export interface TrendData {
  testTrend: {
    date: string
    passed: number
    failed: number
    total: number
  }[]
  defectDistribution: {
    type: string
    count: number
    percentage: number
  }[]
  coverage: {
    module: string
    coverage: number
    target: number
  }[]
}

export interface DashboardData {
  overview: DashboardOverview
  projects: ProjectHealth[]
  recentTests: RecentTest[]
  agentStatus: AgentStatus
  trends: TrendData
}

export const useDashboardData = () => {
  return useQuery<DashboardData, Error>(
    ['dashboard'],
    async () => {
      const response = await dashboardApi.getDashboardData()
      return response.data
    },
    {
      refetchInterval: 30000, // 30秒自动刷新
      staleTime: 10000, // 10秒内认为数据是新鲜的
      cacheTime: 300000, // 5分钟缓存时间
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      onError: (error) => {
        console.error('获取仪表盘数据失败:', error)
      },
    }
  )
}

// 获取项目健康度数据
export const useProjectHealth = () => {
  return useQuery<ProjectHealth[], Error>(
    ['dashboard', 'projects'],
    async () => {
      const response = await dashboardApi.getProjectHealth()
      return response.data
    },
    {
      refetchInterval: 60000, // 1分钟刷新
      staleTime: 30000,
    }
  )
}

// 获取智能体状态
export const useAgentStatus = () => {
  return useQuery<AgentStatus, Error>(
    ['dashboard', 'agents'],
    async () => {
      const response = await dashboardApi.getAgentStatus()
      return response.data
    },
    {
      refetchInterval: 10000, // 10秒刷新
      staleTime: 5000,
    }
  )
}

// 获取测试趋势数据
export const useTestTrends = (timeRange: string = '7d') => {
  return useQuery<TrendData, Error>(
    ['dashboard', 'trends', timeRange],
    async () => {
      const response = await dashboardApi.getTestTrends(timeRange)
      return response.data
    },
    {
      refetchInterval: 300000, // 5分钟刷新
      staleTime: 60000,
    }
  )
}

// 获取实时测试执行数据
export const useRealtimeTests = () => {
  return useQuery<RecentTest[], Error>(
    ['dashboard', 'realtime-tests'],
    async () => {
      const response = await dashboardApi.getRealtimeTests()
      return response.data
    },
    {
      refetchInterval: 5000, // 5秒刷新
      staleTime: 2000,
    }
  )
}
