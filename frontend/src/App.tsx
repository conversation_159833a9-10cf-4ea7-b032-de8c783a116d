import React, { Suspense } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { ConfigProvider, App as AntApp, theme } from 'antd'
import { QueryClient, QueryClientProvider } from 'react-query'
import { ReactQueryDevtools } from 'react-query/devtools'
import { ErrorBoundary } from 'react-error-boundary'
import { HelmetProvider } from 'react-helmet-async'
import zhCN from 'antd/locale/zh_CN'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

// 设置dayjs中文
dayjs.locale('zh-cn')

// 导入样式
import './styles/index.css'
import './styles/antd-custom.css'

// 导入组件
import Layout from '@/components/Layout'
import LoadingSpinner from '@/components/LoadingSpinner'
import ErrorFallback from '@/components/ErrorFallback'
import { useThemeStore } from '@/store/themeStore'

// 懒加载页面组件
const Dashboard = React.lazy(() => import('@/pages/Dashboard'))
const Projects = React.lazy(() => import('@/pages/Projects'))
const TestCases = React.lazy(() => import('@/pages/TestCases'))
const TestExecution = React.lazy(() => import('@/pages/TestExecution'))
const DefectManagement = React.lazy(() => import('@/pages/DefectManagement'))
const Reports = React.lazy(() => import('@/pages/Reports'))
const Settings = React.lazy(() => import('@/pages/Settings'))
const Login = React.lazy(() => import('@/pages/Login'))

// 创建React Query客户端
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5分钟
      cacheTime: 10 * 60 * 1000, // 10分钟
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
})

// 主题配置
const getThemeConfig = (isDark: boolean) => ({
  algorithm: isDark ? theme.darkAlgorithm : theme.defaultAlgorithm,
  token: {
    colorPrimary: '#1E3A8A',
    colorSuccess: '#10B981',
    colorWarning: '#F59E0B',
    colorError: '#EF4444',
    colorInfo: '#06B6D4',
    borderRadius: 8,
    wireframe: false,
    fontSize: 14,
    fontFamily: '"Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  },
  components: {
    Layout: {
      headerBg: isDark ? '#1f2937' : '#ffffff',
      siderBg: isDark ? '#111827' : '#f8fafc',
      bodyBg: isDark ? '#0f172a' : '#ffffff',
    },
    Menu: {
      itemBg: 'transparent',
      itemSelectedBg: isDark ? '#1e40af' : '#e0f2fe',
      itemHoverBg: isDark ? '#374151' : '#f1f5f9',
    },
    Card: {
      headerBg: isDark ? '#1f2937' : '#fafafa',
    },
    Table: {
      headerBg: isDark ? '#1f2937' : '#fafafa',
    },
  },
})

const AppContent: React.FC = () => {
  const { isDark } = useThemeStore()

  return (
    <ConfigProvider
      locale={zhCN}
      theme={getThemeConfig(isDark)}
    >
      <AntApp>
        <HelmetProvider>
          <QueryClientProvider client={queryClient}>
            <ErrorBoundary
              FallbackComponent={ErrorFallback}
              onError={(error, errorInfo) => {
                console.error('应用错误:', error, errorInfo)
                // 这里可以添加错误上报逻辑
              }}
            >
              <Router>
                <Routes>
                  {/* 登录页面 */}
                  <Route
                    path="/login"
                    element={
                      <Suspense fallback={<LoadingSpinner />}>
                        <Login />
                      </Suspense>
                    }
                  />
                  
                  {/* 主应用布局 */}
                  <Route
                    path="/*"
                    element={
                      <Layout>
                        <Suspense fallback={<LoadingSpinner />}>
                          <Routes>
                            {/* 仪表盘 */}
                            <Route path="/" element={<Navigate to="/dashboard" replace />} />
                            <Route path="/dashboard" element={<Dashboard />} />
                            
                            {/* 项目管理 */}
                            <Route path="/projects" element={<Projects />} />
                            <Route path="/projects/:id" element={<Projects />} />
                            
                            {/* 测试用例 */}
                            <Route path="/test-cases" element={<TestCases />} />
                            <Route path="/test-cases/:id" element={<TestCases />} />
                            
                            {/* 测试执行 */}
                            <Route path="/test-execution" element={<TestExecution />} />
                            <Route path="/test-execution/:id" element={<TestExecution />} />
                            
                            {/* 缺陷管理 */}
                            <Route path="/defects" element={<DefectManagement />} />
                            <Route path="/defects/:id" element={<DefectManagement />} />
                            
                            {/* 测试报告 */}
                            <Route path="/reports" element={<Reports />} />
                            <Route path="/reports/:id" element={<Reports />} />
                            
                            {/* 设置 */}
                            <Route path="/settings" element={<Settings />} />
                            <Route path="/settings/:tab" element={<Settings />} />
                            
                            {/* 404页面 */}
                            <Route path="*" element={<Navigate to="/dashboard" replace />} />
                          </Routes>
                        </Suspense>
                      </Layout>
                    }
                  />
                </Routes>
              </Router>
            </ErrorBoundary>
            
            {/* React Query开发工具 */}
            {process.env.NODE_ENV === 'development' && (
              <ReactQueryDevtools initialIsOpen={false} />
            )}
          </QueryClientProvider>
        </HelmetProvider>
      </AntApp>
    </ConfigProvider>
  )
}

const App: React.FC = () => {
  return <AppContent />
}

export default App
