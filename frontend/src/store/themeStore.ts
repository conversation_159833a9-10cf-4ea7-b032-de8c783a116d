import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface ThemeState {
  isDark: boolean
  primaryColor: string
  fontSize: number
  borderRadius: number
  compactMode: boolean
  toggleTheme: () => void
  setPrimaryColor: (color: string) => void
  setFontSize: (size: number) => void
  setBorderRadius: (radius: number) => void
  setCompactMode: (compact: boolean) => void
  resetTheme: () => void
}

// 默认主题配置
const defaultTheme = {
  isDark: false,
  primaryColor: '#1E3A8A',
  fontSize: 14,
  borderRadius: 8,
  compactMode: false,
}

export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      ...defaultTheme,
      
      toggleTheme: () => {
        const { isDark } = get()
        set({ isDark: !isDark })
        
        // 更新HTML类名用于CSS变量
        if (!isDark) {
          document.documentElement.classList.add('dark')
        } else {
          document.documentElement.classList.remove('dark')
        }
      },
      
      setPrimaryColor: (color: string) => {
        set({ primaryColor: color })
        
        // 更新CSS变量
        document.documentElement.style.setProperty('--primary-color', color)
      },
      
      setFontSize: (size: number) => {
        set({ fontSize: size })
        
        // 更新CSS变量
        document.documentElement.style.setProperty('--font-size', `${size}px`)
      },
      
      setBorderRadius: (radius: number) => {
        set({ borderRadius: radius })
        
        // 更新CSS变量
        document.documentElement.style.setProperty('--border-radius', `${radius}px`)
      },
      
      setCompactMode: (compact: boolean) => {
        set({ compactMode: compact })
        
        // 更新HTML类名
        if (compact) {
          document.documentElement.classList.add('compact')
        } else {
          document.documentElement.classList.remove('compact')
        }
      },
      
      resetTheme: () => {
        set(defaultTheme)
        
        // 重置CSS变量和类名
        document.documentElement.classList.remove('dark', 'compact')
        document.documentElement.style.removeProperty('--primary-color')
        document.documentElement.style.removeProperty('--font-size')
        document.documentElement.style.removeProperty('--border-radius')
      },
    }),
    {
      name: 'testgenius-theme',
      onRehydrateStorage: () => (state) => {
        if (state) {
          // 恢复主题状态到DOM
          if (state.isDark) {
            document.documentElement.classList.add('dark')
          }
          
          if (state.compactMode) {
            document.documentElement.classList.add('compact')
          }
          
          document.documentElement.style.setProperty('--primary-color', state.primaryColor)
          document.documentElement.style.setProperty('--font-size', `${state.fontSize}px`)
          document.documentElement.style.setProperty('--border-radius', `${state.borderRadius}px`)
        }
      },
    }
  )
)
