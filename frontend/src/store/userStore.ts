import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { message } from 'antd'
import { authApi } from '@/api/auth'

export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  role: string
  permissions: string[]
  preferences: {
    language: string
    timezone: string
    notifications: boolean
  }
  lastLoginAt: string
  createdAt: string
}

interface UserState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  loading: boolean
  
  // Actions
  login: (credentials: { email: string; password: string }) => Promise<boolean>
  logout: () => void
  updateUser: (userData: Partial<User>) => void
  updatePreferences: (preferences: Partial<User['preferences']>) => void
  refreshToken: () => Promise<boolean>
  checkAuth: () => Promise<boolean>
}

export const useUserStore = create<UserState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      loading: false,
      
      login: async (credentials) => {
        set({ loading: true })
        
        try {
          const response = await authApi.login(credentials)
          const { user, token } = response.data
          
          set({
            user,
            token,
            isAuthenticated: true,
            loading: false,
          })
          
          // 设置axios默认header
          authApi.setAuthToken(token)
          
          message.success('登录成功')
          return true
          
        } catch (error: any) {
          set({ loading: false })
          message.error(error.message || '登录失败')
          return false
        }
      },
      
      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
        })
        
        // 清除axios header
        authApi.clearAuthToken()
        
        // 清除本地存储
        localStorage.removeItem('testgenius-user')
        
        message.info('已退出登录')
        
        // 跳转到登录页
        window.location.href = '/login'
      },
      
      updateUser: (userData) => {
        const { user } = get()
        if (user) {
          set({
            user: { ...user, ...userData }
          })
        }
      },
      
      updatePreferences: async (preferences) => {
        const { user } = get()
        if (!user) return
        
        try {
          const updatedUser = {
            ...user,
            preferences: { ...user.preferences, ...preferences }
          }
          
          // 调用API更新
          await authApi.updateProfile(updatedUser)
          
          set({ user: updatedUser })
          message.success('偏好设置已更新')
          
        } catch (error: any) {
          message.error(error.message || '更新失败')
        }
      },
      
      refreshToken: async () => {
        const { token } = get()
        if (!token) return false
        
        try {
          const response = await authApi.refreshToken(token)
          const { token: newToken, user } = response.data
          
          set({
            token: newToken,
            user,
            isAuthenticated: true,
          })
          
          authApi.setAuthToken(newToken)
          return true
          
        } catch (error) {
          // Token刷新失败，清除认证状态
          get().logout()
          return false
        }
      },
      
      checkAuth: async () => {
        const { token } = get()
        if (!token) return false
        
        try {
          const response = await authApi.getCurrentUser()
          const { user } = response.data
          
          set({
            user,
            isAuthenticated: true,
          })
          
          return true
          
        } catch (error) {
          // 认证失败，清除状态
          get().logout()
          return false
        }
      },
    }),
    {
      name: 'testgenius-user',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        if (state?.token) {
          // 恢复axios认证header
          authApi.setAuthToken(state.token)
          
          // 检查认证状态
          state.checkAuth?.()
        }
      },
    }
  )
)
