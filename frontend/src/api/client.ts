import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { message } from 'antd'

// API响应接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: string
}

// 错误响应接口
export interface ApiError {
  code: number
  message: string
  details?: any
}

// 创建axios实例
const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: import.meta.env.VITE_API_URL || '/api/v1',
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  })

  // 请求拦截器
  client.interceptors.request.use(
    (config) => {
      // 添加认证token
      const token = localStorage.getItem('testgenius-token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }

      // 添加请求ID用于追踪
      config.headers['X-Request-ID'] = generateRequestId()

      // 开发环境下打印请求信息
      if (import.meta.env.DEV) {
        console.log('🚀 API Request:', {
          method: config.method?.toUpperCase(),
          url: config.url,
          data: config.data,
          params: config.params,
        })
      }

      return config
    },
    (error) => {
      console.error('❌ Request Error:', error)
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  client.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      // 开发环境下打印响应信息
      if (import.meta.env.DEV) {
        console.log('✅ API Response:', {
          status: response.status,
          url: response.config.url,
          data: response.data,
        })
      }

      // 检查业务状态码
      if (response.data.code !== 200) {
        const error = new Error(response.data.message || '请求失败')
        ;(error as any).code = response.data.code
        throw error
      }

      return response
    },
    (error) => {
      console.error('❌ Response Error:', error)

      // 处理网络错误
      if (!error.response) {
        message.error('网络连接失败，请检查网络设置')
        return Promise.reject(new Error('网络连接失败'))
      }

      const { status, data } = error.response

      // 处理不同的HTTP状态码
      switch (status) {
        case 401:
          message.error('登录已过期，请重新登录')
          // 清除本地存储并跳转到登录页
          localStorage.removeItem('testgenius-token')
          localStorage.removeItem('testgenius-user')
          window.location.href = '/login'
          break

        case 403:
          message.error('权限不足，无法访问该资源')
          break

        case 404:
          message.error('请求的资源不存在')
          break

        case 422:
          // 表单验证错误
          if (data?.details) {
            const errors = Object.values(data.details).flat()
            message.error(errors.join(', '))
          } else {
            message.error(data?.message || '请求参数错误')
          }
          break

        case 429:
          message.error('请求过于频繁，请稍后再试')
          break

        case 500:
          message.error('服务器内部错误，请联系管理员')
          break

        case 502:
        case 503:
        case 504:
          message.error('服务暂时不可用，请稍后再试')
          break

        default:
          message.error(data?.message || `请求失败 (${status})`)
      }

      return Promise.reject(error)
    }
  )

  return client
}

// 生成请求ID
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// 创建API客户端实例
export const apiClient = createApiClient()

// 通用API方法
export class ApiClient {
  private client: AxiosInstance

  constructor(client: AxiosInstance) {
    this.client = client
  }

  // GET请求
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.client.get(url, config)
  }

  // POST请求
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.client.post(url, data, config)
  }

  // PUT请求
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.client.put(url, data, config)
  }

  // PATCH请求
  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.client.patch(url, data, config)
  }

  // DELETE请求
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> {
    return this.client.delete(url, config)
  }

  // 文件上传
  async upload<T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<AxiosResponse<ApiResponse<T>>> {
    const formData = new FormData()
    formData.append('file', file)

    return this.client.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    })
  }

  // 文件下载
  async download(url: string, filename?: string): Promise<void> {
    const response = await this.client.get(url, {
      responseType: 'blob',
    })

    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  }

  // 设置认证token
  setAuthToken(token: string): void {
    this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`
    localStorage.setItem('testgenius-token', token)
  }

  // 清除认证token
  clearAuthToken(): void {
    delete this.client.defaults.headers.common['Authorization']
    localStorage.removeItem('testgenius-token')
  }

  // 获取当前token
  getAuthToken(): string | null {
    return localStorage.getItem('testgenius-token')
  }
}

// 导出API客户端实例
export const api = new ApiClient(apiClient)

// 默认导出
export default api
