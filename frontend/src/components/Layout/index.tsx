import React, { useState, useEffect } from 'react'
import { Layout as AntLayout, Menu, Avatar, Dropdown, Badge, Button, Space, Drawer } from 'antd'
import { 
  DashboardOutlined,
  ProjectOutlined,
  FileTextOutlined,
  PlayCircleOutlined,
  BugOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
  BellOutlined,
  MenuOutlined,
  SunOutlined,
  MoonOutlined,
  RobotOutlined
} from '@ant-design/icons'
import { useNavigate, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import classNames from 'classnames'

// 导入store和hooks
import { useThemeStore } from '@/store/themeStore'
import { useUserStore } from '@/store/userStore'
import { useNotificationStore } from '@/store/notificationStore'

const { Header, Sider, Content } = AntLayout

interface LayoutProps {
  children: React.ReactNode
}

// 菜单配置
const menuItems = [
  {
    key: '/dashboard',
    icon: <DashboardOutlined />,
    label: '仪表盘',
  },
  {
    key: '/projects',
    icon: <ProjectOutlined />,
    label: '项目管理',
  },
  {
    key: '/test-cases',
    icon: <FileTextOutlined />,
    label: '测试用例',
  },
  {
    key: '/test-execution',
    icon: <PlayCircleOutlined />,
    label: '测试执行',
  },
  {
    key: '/defects',
    icon: <BugOutlined />,
    label: '缺陷管理',
  },
  {
    key: '/reports',
    icon: <BarChartOutlined />,
    label: '测试报告',
  },
  {
    key: '/settings',
    icon: <SettingOutlined />,
    label: '系统设置',
  },
]

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const navigate = useNavigate()
  const location = useLocation()
  
  const [collapsed, setCollapsed] = useState(false)
  const [mobileDrawerVisible, setMobileDrawerVisible] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  // Store hooks
  const { isDark, toggleTheme } = useThemeStore()
  const { user, logout } = useUserStore()
  const { notifications, unreadCount } = useNotificationStore()

  // 响应式处理
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768
      setIsMobile(mobile)
      if (!mobile) {
        setMobileDrawerVisible(false)
      }
    }

    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // 菜单点击处理
  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key)
    if (isMobile) {
      setMobileDrawerVisible(false)
    }
  }

  // 用户菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => navigate('/settings/profile'),
    },
    {
      key: 'preferences',
      icon: <SettingOutlined />,
      label: '偏好设置',
      onClick: () => navigate('/settings/preferences'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: logout,
    },
  ]

  // 侧边栏内容
  const siderContent = (
    <div className="h-full flex flex-col">
      {/* Logo区域 */}
      <div className={classNames(
        'flex items-center justify-center py-4 border-b border-gray-200 dark:border-gray-700',
        collapsed ? 'px-2' : 'px-4'
      )}>
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          className="flex items-center space-x-2"
        >
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <RobotOutlined className="text-white text-lg" />
          </div>
          {!collapsed && (
            <div className="text-lg font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              TestGenius AI
            </div>
          )}
        </motion.div>
      </div>

      {/* 菜单区域 */}
      <div className="flex-1 overflow-y-auto">
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          className="border-none"
          inlineCollapsed={collapsed}
        />
      </div>

      {/* 底部信息 */}
      {!collapsed && (
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-xs text-gray-500 text-center">
            <div>TestGenius AI v1.0.0</div>
            <div>Powered by Autogen</div>
          </div>
        </div>
      )}
    </div>
  )

  return (
    <AntLayout className="min-h-screen">
      {/* 桌面端侧边栏 */}
      {!isMobile && (
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          width={240}
          className="shadow-lg"
          theme={isDark ? 'dark' : 'light'}
        >
          {siderContent}
        </Sider>
      )}

      {/* 移动端抽屉 */}
      {isMobile && (
        <Drawer
          title="TestGenius AI"
          placement="left"
          onClose={() => setMobileDrawerVisible(false)}
          open={mobileDrawerVisible}
          bodyStyle={{ padding: 0 }}
          width={240}
        >
          {siderContent}
        </Drawer>
      )}

      <AntLayout>
        {/* 顶部导航栏 */}
        <Header className="bg-white dark:bg-gray-800 shadow-sm px-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* 折叠按钮 */}
            <Button
              type="text"
              icon={isMobile ? <MenuOutlined /> : (collapsed ? <MenuOutlined /> : <MenuOutlined />)}
              onClick={() => {
                if (isMobile) {
                  setMobileDrawerVisible(true)
                } else {
                  setCollapsed(!collapsed)
                }
              }}
              className="text-lg"
            />

            {/* 面包屑或页面标题 */}
            <div className="text-lg font-medium">
              {menuItems.find(item => item.key === location.pathname)?.label || '页面'}
            </div>
          </div>

          {/* 右侧操作区 */}
          <Space size="middle">
            {/* 主题切换 */}
            <Button
              type="text"
              icon={isDark ? <SunOutlined /> : <MoonOutlined />}
              onClick={toggleTheme}
              className="text-lg"
            />

            {/* 通知 */}
            <Badge count={unreadCount} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                className="text-lg"
              />
            </Badge>

            {/* 用户菜单 */}
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              trigger={['click']}
            >
              <div className="flex items-center space-x-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded">
                <Avatar
                  size="small"
                  src={user?.avatar}
                  icon={<UserOutlined />}
                />
                <span className="hidden sm:inline text-sm">
                  {user?.name || '用户'}
                </span>
              </div>
            </Dropdown>
          </Space>
        </Header>

        {/* 主内容区 */}
        <Content className="bg-gray-50 dark:bg-gray-900 min-h-[calc(100vh-64px)]">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="h-full"
          >
            {children}
          </motion.div>
        </Content>
      </AntLayout>
    </AntLayout>
  )
}

export default Layout
