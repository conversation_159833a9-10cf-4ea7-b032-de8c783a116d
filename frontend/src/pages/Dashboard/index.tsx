import React, { useState, useEffect } from 'react'
import { Row, Col, Card, Statistic, Progress, Button, Space, Typography, Spin, Alert } from 'antd'
import { 
  RocketOutlined, 
  BugOutlined, 
  CheckCircleOutlined, 
  ClockCircleOutlined,
  TrophyOutlined,
  TeamOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  ReloadOutlined
} from '@ant-design/icons'
import { motion } from 'framer-motion'
import { Helmet } from 'react-helmet-async'

// 导入组件
import TestTrendChart from '@/components/Charts/TestTrendChart'
import DefectDistributionChart from '@/components/Charts/DefectDistributionChart'
import CoverageChart from '@/components/Charts/CoverageChart'
import RecentActivities from '@/components/Dashboard/RecentActivities'
import QuickActions from '@/components/Dashboard/QuickActions'
import ProjectHealthCards from '@/components/Dashboard/ProjectHealthCards'
import AgentStatusPanel from '@/components/Dashboard/AgentStatusPanel'

// 导入hooks和store
import { useDashboardData } from '@/hooks/useDashboardData'
import { useWebSocket } from '@/hooks/useWebSocket'

const { Title, Text } = Typography

// 动画配置
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
}

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100
    }
  }
}

const Dashboard: React.FC = () => {
  const [refreshing, setRefreshing] = useState(false)
  
  // 获取仪表盘数据
  const {
    data: dashboardData,
    loading,
    error,
    refetch
  } = useDashboardData()

  // WebSocket连接用于实时更新
  const { connected, lastMessage } = useWebSocket('/ws/dashboard')

  // 处理数据刷新
  const handleRefresh = async () => {
    setRefreshing(true)
    try {
      await refetch()
    } finally {
      setRefreshing(false)
    }
  }

  // 实时数据更新
  useEffect(() => {
    if (lastMessage) {
      // 处理WebSocket消息，更新实时数据
      console.log('收到实时数据:', lastMessage)
    }
  }, [lastMessage])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <Spin size="large" tip="加载仪表盘数据..." />
      </div>
    )
  }

  if (error) {
    return (
      <Alert
        message="数据加载失败"
        description={error.message}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={handleRefresh}>
            重试
          </Button>
        }
      />
    )
  }

  const {
    overview = {},
    projects = [],
    recentTests = [],
    agentStatus = {},
    trends = {}
  } = dashboardData || {}

  return (
    <>
      <Helmet>
        <title>仪表盘 - TestGenius AI</title>
        <meta name="description" content="TestGenius AI 智能测试平台仪表盘" />
      </Helmet>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="p-6 space-y-6"
      >
        {/* 页面标题和操作 */}
        <motion.div variants={itemVariants} className="flex justify-between items-center">
          <div>
            <Title level={2} className="mb-2">
              🚀 TestGenius AI 仪表盘
            </Title>
            <Text type="secondary">
              智能测试平台总览 • 实时监控 • 数据洞察
              {connected && (
                <span className="ml-2 text-green-500">
                  ● 实时连接
                </span>
              )}
            </Text>
          </div>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              loading={refreshing}
              onClick={handleRefresh}
            >
              刷新数据
            </Button>
            <QuickActions />
          </Space>
        </motion.div>

        {/* 核心指标卡片 */}
        <motion.div variants={itemVariants}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={6}>
              <Card className="text-center hover:shadow-lg transition-shadow">
                <Statistic
                  title="活跃项目"
                  value={overview.activeProjects || 0}
                  prefix={<RocketOutlined className="text-blue-500" />}
                  valueStyle={{ color: '#1E3A8A' }}
                />
                <Progress
                  percent={overview.projectHealthScore || 0}
                  size="small"
                  strokeColor="#10B981"
                  className="mt-2"
                />
              </Card>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <Card className="text-center hover:shadow-lg transition-shadow">
                <Statistic
                  title="执行中测试"
                  value={overview.runningTests || 0}
                  prefix={<ClockCircleOutlined className="text-orange-500" />}
                  valueStyle={{ color: '#F59E0B' }}
                />
                <Text type="secondary" className="block mt-2">
                  预计完成: {overview.estimatedCompletion || '计算中...'}
                </Text>
              </Card>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <Card className="text-center hover:shadow-lg transition-shadow">
                <Statistic
                  title="今日缺陷"
                  value={overview.todayDefects || 0}
                  prefix={<BugOutlined className="text-red-500" />}
                  valueStyle={{ color: '#EF4444' }}
                />
                <Text type="secondary" className="block mt-2">
                  已修复: {overview.fixedDefects || 0}
                </Text>
              </Card>
            </Col>
            
            <Col xs={24} sm={12} lg={6}>
              <Card className="text-center hover:shadow-lg transition-shadow">
                <Statistic
                  title="测试覆盖率"
                  value={overview.coverageRate || 0}
                  suffix="%"
                  prefix={<TrophyOutlined className="text-green-500" />}
                  valueStyle={{ color: '#10B981' }}
                />
                <Progress
                  percent={overview.coverageRate || 0}
                  size="small"
                  strokeColor="#10B981"
                  className="mt-2"
                />
              </Card>
            </Col>
          </Row>
        </motion.div>

        {/* 智能体状态面板 */}
        <motion.div variants={itemVariants}>
          <AgentStatusPanel agentStatus={agentStatus} />
        </motion.div>

        {/* 图表区域 */}
        <motion.div variants={itemVariants}>
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={16}>
              <Card 
                title={
                  <Space>
                    <BarChartOutlined />
                    测试趋势分析
                  </Space>
                }
                className="h-96"
              >
                <TestTrendChart data={trends.testTrend} />
              </Card>
            </Col>
            
            <Col xs={24} lg={8}>
              <Card 
                title="缺陷分布"
                className="h-96"
              >
                <DefectDistributionChart data={trends.defectDistribution} />
              </Card>
            </Col>
          </Row>
        </motion.div>

        {/* 项目健康度和覆盖率 */}
        <motion.div variants={itemVariants}>
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={16}>
              <ProjectHealthCards projects={projects} />
            </Col>
            
            <Col xs={24} lg={8}>
              <Card 
                title="覆盖率分析"
                className="h-80"
              >
                <CoverageChart data={trends.coverage} />
              </Card>
            </Col>
          </Row>
        </motion.div>

        {/* 最近活动 */}
        <motion.div variants={itemVariants}>
          <RecentActivities activities={recentTests} />
        </motion.div>

        {/* 快速统计 */}
        <motion.div variants={itemVariants}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={8}>
              <Card className="text-center">
                <Statistic
                  title="团队成员"
                  value={overview.teamMembers || 0}
                  prefix={<TeamOutlined />}
                />
              </Card>
            </Col>
            
            <Col xs={24} sm={8}>
              <Card className="text-center">
                <Statistic
                  title="本周测试"
                  value={overview.weeklyTests || 0}
                  prefix={<CheckCircleOutlined />}
                />
              </Card>
            </Col>
            
            <Col xs={24} sm={8}>
              <Card className="text-center">
                <Statistic
                  title="成功率"
                  value={overview.successRate || 0}
                  suffix="%"
                  prefix={<TrophyOutlined />}
                />
              </Card>
            </Col>
          </Row>
        </motion.div>
      </motion.div>
    </>
  )
}

export default Dashboard
