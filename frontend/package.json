{"name": "testgenius-ai-frontend", "version": "1.0.0", "description": "TestGenius AI - 智能测试平台前端应用", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "@ant-design/colors": "^7.0.2", "zustand": "^4.4.7", "axios": "^1.6.2", "dayjs": "^1.11.10", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "d3": "^7.8.5", "@types/d3": "^7.4.3", "framer-motion": "^10.16.16", "react-beautiful-dnd": "^13.1.1", "@types/react-beautiful-dnd": "^13.1.8", "react-monaco-editor": "^0.54.0", "monaco-editor": "^0.45.0", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "react-syntax-highlighter": "^15.5.0", "@types/react-syntax-highlighter": "^15.5.11", "react-hotkeys-hook": "^4.4.1", "react-use": "^17.4.2", "lodash-es": "^4.17.21", "@types/lodash-es": "^4.17.12", "classnames": "^2.3.2", "react-helmet-async": "^2.0.4", "react-error-boundary": "^4.0.11", "react-virtualized-auto-sizer": "^1.0.24", "react-window": "^1.8.8", "@types/react-window": "^1.8.8", "react-intersection-observer": "^9.5.3", "react-dropzone": "^14.2.3", "file-saver": "^2.0.5", "@types/file-saver": "^2.0.7", "js-cookie": "^3.0.5", "@types/js-cookie": "^3.0.6", "socket.io-client": "^4.7.4", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "react-table": "^7.8.0", "@types/react-table": "^7.7.19", "react-select": "^5.8.0", "react-color": "^2.19.3", "@types/react-color": "^3.0.10", "react-grid-layout": "^1.4.4", "@types/react-grid-layout": "^1.3.5", "react-split-pane": "^0.1.92", "@types/react-split-pane": "^0.1.71"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "vite": "^5.0.8", "typescript": "^5.2.2", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "prettier": "^3.1.1", "vitest": "^1.0.4", "@vitest/ui": "^1.0.4", "jsdom": "^23.0.1", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite-plugin-eslint": "^1.8.1", "vite-plugin-windicss": "^1.9.3", "windicss": "^3.5.6", "rollup-plugin-visualizer": "^5.12.0", "@types/node": "^20.10.5", "cross-env": "^7.0.3", "husky": "^8.0.3", "lint-staged": "^15.2.0", "concurrently": "^8.2.2"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run type-check && npm run test"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}