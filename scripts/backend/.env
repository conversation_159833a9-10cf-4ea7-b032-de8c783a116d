# 基础配置
DEBUG=true
SECRET_KEY=your-secret-key-change-in-production
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=postgresql://testgenius:testgenius123@localhost:5432/testgenius
REDIS_URL=redis://localhost:6379/0

# AI模型配置 (请填入您的API密钥)
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_TEMPERATURE=0.1

# Azure OpenAI配置 (可选)
# AZURE_OPENAI_API_KEY=your-azure-openai-key
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
# AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4

# 测试执行配置
SELENIUM_HUB_URL=http://localhost:4444/wd/hub
PLAYWRIGHT_HEADLESS=true
MAX_PARALLEL_TESTS=5

# 文件存储配置
UPLOAD_DIR=uploads
ARTIFACTS_DIR=artifacts
MAX_UPLOAD_SIZE=104857600

# 安全配置
JWT_SECRET_KEY=jwt-secret-key-change-in-production
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# 监控配置
PROMETHEUS_ENABLED=true
LOG_LEVEL=INFO
