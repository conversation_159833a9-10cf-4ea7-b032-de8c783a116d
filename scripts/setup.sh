#!/bin/bash

# TestGenius AI 项目设置脚本
# 用于快速设置开发环境和启动项目

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

print_header() {
    echo -e "\n${PURPLE}================================${NC}"
    echo -e "${PURPLE}  $1${NC}"
    echo -e "${PURPLE}================================${NC}\n"
}

print_step() {
    echo -e "${BLUE}[步骤] $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_error "$1 未安装，请先安装 $1"
        exit 1
    fi
}

# 检查系统要求
check_requirements() {
    print_header "检查系统要求"
    
    print_step "检查 Python 版本..."
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        print_success "Python $PYTHON_VERSION 已安装"
    else
        print_error "Python 3.9+ 未安装"
        exit 1
    fi
    
    print_step "检查 Node.js 版本..."
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js $NODE_VERSION 已安装"
    else
        print_error "Node.js 18+ 未安装"
        exit 1
    fi
    
    print_step "检查 Docker..."
    if command -v docker &> /dev/null; then
        print_success "Docker 已安装"
    else
        print_warning "Docker 未安装，将跳过容器化部署"
    fi
    
    print_step "检查 Git..."
    check_command git
    print_success "Git 已安装"
}

# 创建环境配置文件
create_env_files() {
    print_header "创建环境配置文件"
    
    # 后端环境配置
    if [ ! -f "backend/.env" ]; then
        print_step "创建后端环境配置..."
        cat > backend/.env << EOF
# 基础配置
DEBUG=true
SECRET_KEY=your-secret-key-change-in-production
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=postgresql://testgenius:testgenius123@localhost:5432/testgenius
REDIS_URL=redis://localhost:6379/0

# AI模型配置 (请填入您的API密钥)
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_TEMPERATURE=0.1

# Azure OpenAI配置 (可选)
# AZURE_OPENAI_API_KEY=your-azure-openai-key
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
# AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4

# 测试执行配置
SELENIUM_HUB_URL=http://localhost:4444/wd/hub
PLAYWRIGHT_HEADLESS=true
MAX_PARALLEL_TESTS=5

# 文件存储配置
UPLOAD_DIR=uploads
ARTIFACTS_DIR=artifacts
MAX_UPLOAD_SIZE=104857600

# 安全配置
JWT_SECRET_KEY=jwt-secret-key-change-in-production
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# 监控配置
PROMETHEUS_ENABLED=true
LOG_LEVEL=INFO
EOF
        print_success "后端环境配置已创建"
    else
        print_warning "后端环境配置已存在，跳过创建"
    fi
    
    # 前端环境配置
    if [ ! -f "frontend/.env" ]; then
        print_step "创建前端环境配置..."
        cat > frontend/.env << EOF
# API配置
VITE_API_URL=http://localhost:8000/api/v1
VITE_WS_URL=ws://localhost:8000/ws

# 应用配置
VITE_APP_TITLE=TestGenius AI
VITE_APP_DESCRIPTION=智能测试平台

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=true
EOF
        print_success "前端环境配置已创建"
    else
        print_warning "前端环境配置已存在，跳过创建"
    fi
}

# 安装依赖
install_dependencies() {
    print_header "安装项目依赖"
    
    # 安装后端依赖
    print_step "安装后端依赖..."
    cd backend
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        print_success "Python虚拟环境已创建"
    fi
    
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r requirements.txt
    print_success "后端依赖安装完成"
    cd ..
    
    # 安装前端依赖
    print_step "安装前端依赖..."
    cd frontend
    npm install
    print_success "前端依赖安装完成"
    cd ..
}

# 初始化数据库
init_database() {
    print_header "初始化数据库"
    
    if command -v docker &> /dev/null; then
        print_step "启动数据库容器..."
        docker-compose up -d postgres redis
        sleep 5
        print_success "数据库容器已启动"
    else
        print_warning "Docker未安装，请手动启动PostgreSQL和Redis"
        print_warning "PostgreSQL: localhost:5432, 数据库: testgenius, 用户: testgenius, 密码: testgenius123"
        print_warning "Redis: localhost:6379"
    fi
    
    print_step "运行数据库迁移..."
    cd backend
    source venv/bin/activate
    # 这里应该运行实际的数据库迁移命令
    # python manage.py migrate
    print_success "数据库初始化完成"
    cd ..
}

# 启动开发服务器
start_dev_servers() {
    print_header "启动开发服务器"
    
    print_step "启动后端服务器..."
    cd backend
    source venv/bin/activate
    nohup uvicorn main:app --host 0.0.0.0 --port 8000 --reload > ../logs/backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > ../logs/backend.pid
    print_success "后端服务器已启动 (PID: $BACKEND_PID)"
    cd ..
    
    sleep 3
    
    print_step "启动前端开发服务器..."
    cd frontend
    nohup npm run dev > ../logs/frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > ../logs/frontend.pid
    print_success "前端开发服务器已启动 (PID: $FRONTEND_PID)"
    cd ..
}

# 显示访问信息
show_access_info() {
    print_header "访问信息"
    
    echo -e "${CYAN}🌐 前端应用: ${NC}http://localhost:3000"
    echo -e "${CYAN}🔧 后端API: ${NC}http://localhost:8000"
    echo -e "${CYAN}📚 API文档: ${NC}http://localhost:8000/docs"
    echo -e "${CYAN}📊 监控面板: ${NC}http://localhost:3001 (Grafana)"
    echo -e "${CYAN}🔍 日志查看: ${NC}http://localhost:5601 (Kibana)"
    
    echo -e "\n${YELLOW}📝 重要提示:${NC}"
    echo -e "1. 请在 backend/.env 中配置您的 OpenAI API 密钥"
    echo -e "2. 首次使用需要创建管理员账户"
    echo -e "3. 查看日志: tail -f logs/backend.log 或 logs/frontend.log"
    echo -e "4. 停止服务: ./scripts/stop.sh"
}

# 创建停止脚本
create_stop_script() {
    cat > scripts/stop.sh << 'EOF'
#!/bin/bash

# 停止开发服务器
echo "正在停止 TestGenius AI 开发服务器..."

# 停止后端服务器
if [ -f "logs/backend.pid" ]; then
    BACKEND_PID=$(cat logs/backend.pid)
    if kill -0 $BACKEND_PID 2>/dev/null; then
        kill $BACKEND_PID
        echo "✅ 后端服务器已停止"
    fi
    rm -f logs/backend.pid
fi

# 停止前端服务器
if [ -f "logs/frontend.pid" ]; then
    FRONTEND_PID=$(cat logs/frontend.pid)
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        kill $FRONTEND_PID
        echo "✅ 前端服务器已停止"
    fi
    rm -f logs/frontend.pid
fi

echo "🎉 所有服务已停止"
EOF
    chmod +x scripts/stop.sh
}

# 主函数
main() {
    print_header "TestGenius AI 项目设置"
    print_message "欢迎使用 TestGenius AI 智能测试平台！" $CYAN
    
    # 创建必要的目录
    mkdir -p logs
    mkdir -p backend/uploads
    mkdir -p backend/artifacts
    
    # 执行设置步骤
    check_requirements
    create_env_files
    install_dependencies
    init_database
    create_stop_script
    start_dev_servers
    
    sleep 5
    show_access_info
    
    print_header "设置完成"
    print_success "TestGenius AI 开发环境已成功设置并启动！"
    print_message "开始您的智能测试之旅吧！🚀" $GREEN
}

# 运行主函数
main "$@"
