"""
TestGenius AI - 主应用入口
基于FastAPI和Microsoft Autogen框架的智能测试平台
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from contextlib import asynccontextmanager
import uvicorn
import logging
from pathlib import Path

from core.config import settings
from core.database import engine, Base
from core.middleware import (
    RequestLoggingMiddleware,
    SecurityHeadersMiddleware,
    RateLimitMiddleware
)
from api.v1.router import api_router
from api.websocket import websocket_router
from agents.orchestrator import AgentOrchestrator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 全局智能体编排器
orchestrator = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global orchestrator
    
    # 启动时初始化
    logger.info("🚀 TestGenius AI 启动中...")
    
    # 创建数据库表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # 初始化智能体编排器
    orchestrator = AgentOrchestrator()
    await orchestrator.initialize()
    
    # 创建必要的目录
    Path("artifacts").mkdir(exist_ok=True)
    Path("uploads").mkdir(exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    
    logger.info("✅ TestGenius AI 启动完成")
    
    yield
    
    # 关闭时清理
    logger.info("🔄 TestGenius AI 关闭中...")
    if orchestrator:
        await orchestrator.cleanup()
    logger.info("✅ TestGenius AI 已关闭")

# 创建FastAPI应用
app = FastAPI(
    title="TestGenius AI",
    description="基于Microsoft Autogen框架的智能测试平台",
    version="1.0.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(RateLimitMiddleware)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")
app.mount("/artifacts", StaticFiles(directory="artifacts"), name="artifacts")

# 注册路由
app.include_router(api_router, prefix="/api/v1")
app.include_router(websocket_router, prefix="/ws")

@app.get("/", response_class=HTMLResponse)
async def root():
    """根路径 - 返回欢迎页面"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>TestGenius AI</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #1E3A8A 0%, #06B6D4 100%);
                color: white;
                margin: 0;
                padding: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
            }
            .container {
                text-align: center;
                padding: 2rem;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 20px;
                backdrop-filter: blur(10px);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            }
            h1 {
                font-size: 3rem;
                margin-bottom: 1rem;
                background: linear-gradient(45deg, #10B981, #06B6D4);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
            .subtitle {
                font-size: 1.2rem;
                margin-bottom: 2rem;
                opacity: 0.9;
            }
            .links {
                display: flex;
                gap: 1rem;
                justify-content: center;
                flex-wrap: wrap;
            }
            .link {
                padding: 0.8rem 1.5rem;
                background: rgba(255, 255, 255, 0.2);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 10px;
                text-decoration: none;
                color: white;
                transition: all 0.3s ease;
            }
            .link:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: translateY(-2px);
            }
            .status {
                margin-top: 2rem;
                padding: 1rem;
                background: rgba(16, 185, 129, 0.2);
                border-radius: 10px;
                border: 1px solid rgba(16, 185, 129, 0.3);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 TestGenius AI</h1>
            <p class="subtitle">基于Microsoft Autogen框架的智能测试平台</p>
            <div class="links">
                <a href="/docs" class="link">📚 API文档</a>
                <a href="/redoc" class="link">📖 ReDoc</a>
                <a href="/api/v1/health" class="link">💚 健康检查</a>
            </div>
            <div class="status">
                <strong>🟢 系统状态：运行中</strong><br>
                <small>智能体编排器已就绪，准备处理测试任务</small>
            </div>
        </div>
    </body>
    </html>
    """

@app.get("/api/v1/health")
async def health_check():
    """健康检查端点"""
    global orchestrator
    
    agent_status = "ready" if orchestrator and orchestrator.is_ready else "initializing"
    
    return {
        "status": "healthy",
        "version": "1.0.0",
        "timestamp": "2024-01-01T00:00:00Z",
        "services": {
            "api": "running",
            "database": "connected",
            "redis": "connected",
            "agents": agent_status
        },
        "message": "TestGenius AI is running smoothly! 🚀"
    }

@app.get("/api/v1/agents/status")
async def get_agents_status():
    """获取智能体状态"""
    global orchestrator
    
    if not orchestrator:
        raise HTTPException(status_code=503, detail="智能体编排器未初始化")
    
    return await orchestrator.get_status()

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info"
    )
