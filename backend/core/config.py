"""
配置管理模块
使用Pydantic Settings进行环境变量管理
"""

from pydantic_settings import BaseSettings
from pydantic import Field, validator
from typing import List, Optional
import os
from pathlib import Path

class Settings(BaseSettings):
    """应用配置类"""
    
    # 基础配置
    APP_NAME: str = "TestGenius AI"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = Field(default=False, env="DEBUG")
    SECRET_KEY: str = Field(default="your-secret-key-here", env="SECRET_KEY")
    
    # 服务器配置
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    ALLOWED_HOSTS: List[str] = Field(default=["*"], env="ALLOWED_HOSTS")
    
    # 数据库配置
    DATABASE_URL: str = Field(
        default="postgresql://testgenius:testgenius123@localhost:5432/testgenius",
        env="DATABASE_URL"
    )
    DATABASE_POOL_SIZE: int = Field(default=20, env="DATABASE_POOL_SIZE")
    DATABASE_MAX_OVERFLOW: int = Field(default=30, env="DATABASE_MAX_OVERFLOW")
    
    # Redis配置
    REDIS_URL: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    REDIS_CACHE_TTL: int = Field(default=3600, env="REDIS_CACHE_TTL")
    
    # Celery配置
    CELERY_BROKER_URL: str = Field(default="redis://localhost:6379/1", env="CELERY_BROKER_URL")
    CELERY_RESULT_BACKEND: str = Field(default="redis://localhost:6379/2", env="CELERY_RESULT_BACKEND")
    
    # AI模型配置
    OPENAI_API_KEY: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    OPENAI_MODEL: str = Field(default="gpt-4-turbo-preview", env="OPENAI_MODEL")
    OPENAI_TEMPERATURE: float = Field(default=0.1, env="OPENAI_TEMPERATURE")
    OPENAI_MAX_TOKENS: int = Field(default=4000, env="OPENAI_MAX_TOKENS")
    
    # Azure OpenAI配置
    AZURE_OPENAI_API_KEY: Optional[str] = Field(default=None, env="AZURE_OPENAI_API_KEY")
    AZURE_OPENAI_ENDPOINT: Optional[str] = Field(default=None, env="AZURE_OPENAI_ENDPOINT")
    AZURE_OPENAI_API_VERSION: str = Field(default="2024-02-01", env="AZURE_OPENAI_API_VERSION")
    AZURE_OPENAI_DEPLOYMENT_NAME: str = Field(default="gpt-4", env="AZURE_OPENAI_DEPLOYMENT_NAME")
    
    # Autogen配置
    AUTOGEN_CACHE_SEED: int = Field(default=42, env="AUTOGEN_CACHE_SEED")
    AUTOGEN_MAX_ROUND: int = Field(default=10, env="AUTOGEN_MAX_ROUND")
    AUTOGEN_TIMEOUT: int = Field(default=300, env="AUTOGEN_TIMEOUT")
    
    # 测试执行配置
    SELENIUM_HUB_URL: str = Field(default="http://localhost:4444/wd/hub", env="SELENIUM_HUB_URL")
    PLAYWRIGHT_HEADLESS: bool = Field(default=True, env="PLAYWRIGHT_HEADLESS")
    TEST_TIMEOUT: int = Field(default=300, env="TEST_TIMEOUT")
    MAX_PARALLEL_TESTS: int = Field(default=5, env="MAX_PARALLEL_TESTS")
    
    # 文件存储配置
    UPLOAD_DIR: str = Field(default="uploads", env="UPLOAD_DIR")
    ARTIFACTS_DIR: str = Field(default="artifacts", env="ARTIFACTS_DIR")
    MAX_UPLOAD_SIZE: int = Field(default=100 * 1024 * 1024, env="MAX_UPLOAD_SIZE")  # 100MB
    ALLOWED_EXTENSIONS: List[str] = Field(
        default=[".pdf", ".docx", ".txt", ".json", ".yaml", ".yml", ".py", ".js"],
        env="ALLOWED_EXTENSIONS"
    )
    
    # 对象存储配置 (MinIO/S3)
    MINIO_ENDPOINT: str = Field(default="localhost:9000", env="MINIO_ENDPOINT")
    MINIO_ACCESS_KEY: str = Field(default="testgenius", env="MINIO_ACCESS_KEY")
    MINIO_SECRET_KEY: str = Field(default="testgenius123", env="MINIO_SECRET_KEY")
    MINIO_BUCKET: str = Field(default="testgenius", env="MINIO_BUCKET")
    MINIO_SECURE: bool = Field(default=False, env="MINIO_SECURE")
    
    # JWT配置
    JWT_SECRET_KEY: str = Field(default="jwt-secret-key", env="JWT_SECRET_KEY")
    JWT_ALGORITHM: str = Field(default="HS256", env="JWT_ALGORITHM")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, env="JWT_ACCESS_TOKEN_EXPIRE_MINUTES")
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default=7, env="JWT_REFRESH_TOKEN_EXPIRE_DAYS")
    
    # 邮件配置
    SMTP_HOST: Optional[str] = Field(default=None, env="SMTP_HOST")
    SMTP_PORT: int = Field(default=587, env="SMTP_PORT")
    SMTP_USERNAME: Optional[str] = Field(default=None, env="SMTP_USERNAME")
    SMTP_PASSWORD: Optional[str] = Field(default=None, env="SMTP_PASSWORD")
    SMTP_TLS: bool = Field(default=True, env="SMTP_TLS")
    
    # 监控配置
    PROMETHEUS_ENABLED: bool = Field(default=True, env="PROMETHEUS_ENABLED")
    PROMETHEUS_PORT: int = Field(default=8001, env="PROMETHEUS_PORT")
    
    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FORMAT: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT"
    )
    LOG_FILE: Optional[str] = Field(default=None, env="LOG_FILE")
    
    # 安全配置
    CORS_ORIGINS: List[str] = Field(default=["*"], env="CORS_ORIGINS")
    RATE_LIMIT_REQUESTS: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    RATE_LIMIT_WINDOW: int = Field(default=60, env="RATE_LIMIT_WINDOW")
    
    # 缓存配置
    CACHE_TTL: int = Field(default=3600, env="CACHE_TTL")
    CACHE_PREFIX: str = Field(default="testgenius:", env="CACHE_PREFIX")
    
    # 任务队列配置
    TASK_QUEUE_NAME: str = Field(default="testgenius_tasks", env="TASK_QUEUE_NAME")
    TASK_RESULT_EXPIRES: int = Field(default=3600, env="TASK_RESULT_EXPIRES")
    
    # 测试报告配置
    REPORT_TEMPLATE_DIR: str = Field(default="templates/reports", env="REPORT_TEMPLATE_DIR")
    REPORT_OUTPUT_DIR: str = Field(default="artifacts/reports", env="REPORT_OUTPUT_DIR")
    
    # 智能体配置
    AGENT_MEMORY_SIZE: int = Field(default=1000, env="AGENT_MEMORY_SIZE")
    AGENT_MAX_ITERATIONS: int = Field(default=50, env="AGENT_MAX_ITERATIONS")
    AGENT_THINKING_TIME: float = Field(default=1.0, env="AGENT_THINKING_TIME")
    
    @validator("ALLOWED_HOSTS", pre=True)
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    @validator("CORS_ORIGINS", pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("ALLOWED_EXTENSIONS", pre=True)
    def parse_allowed_extensions(cls, v):
        if isinstance(v, str):
            return [ext.strip() for ext in v.split(",")]
        return v
    
    @property
    def database_url_sync(self) -> str:
        """同步数据库URL"""
        return self.DATABASE_URL.replace("postgresql://", "postgresql+psycopg2://")
    
    @property
    def database_url_async(self) -> str:
        """异步数据库URL"""
        return self.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")
    
    def get_openai_config(self) -> dict:
        """获取OpenAI配置"""
        if self.AZURE_OPENAI_API_KEY and self.AZURE_OPENAI_ENDPOINT:
            return {
                "model": self.AZURE_OPENAI_DEPLOYMENT_NAME,
                "api_key": self.AZURE_OPENAI_API_KEY,
                "base_url": f"{self.AZURE_OPENAI_ENDPOINT}/openai/deployments/{self.AZURE_OPENAI_DEPLOYMENT_NAME}",
                "api_type": "azure",
                "api_version": self.AZURE_OPENAI_API_VERSION,
                "temperature": self.OPENAI_TEMPERATURE,
                "max_tokens": self.OPENAI_MAX_TOKENS,
            }
        elif self.OPENAI_API_KEY:
            return {
                "model": self.OPENAI_MODEL,
                "api_key": self.OPENAI_API_KEY,
                "temperature": self.OPENAI_TEMPERATURE,
                "max_tokens": self.OPENAI_MAX_TOKENS,
            }
        else:
            raise ValueError("未配置OpenAI或Azure OpenAI API密钥")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

# 创建全局配置实例
settings = Settings()

# 确保必要的目录存在
Path(settings.UPLOAD_DIR).mkdir(exist_ok=True)
Path(settings.ARTIFACTS_DIR).mkdir(exist_ok=True)
Path(settings.REPORT_OUTPUT_DIR).mkdir(parents=True, exist_ok=True)
Path("logs").mkdir(exist_ok=True)
