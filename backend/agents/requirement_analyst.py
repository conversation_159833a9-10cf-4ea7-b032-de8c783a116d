"""
需求理解智能体 - 负责解析和分析测试需求
"""

import re
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from .base_agent import BaseTestAgent
from core.config import settings

logger = logging.getLogger(__name__)

class RequirementAnalystAgent(BaseTestAgent):
    """需求分析智能体"""
    
    def __init__(self, name: str, system_message: str, llm_config: Dict[str, Any], **kwargs):
        super().__init__(name, system_message, llm_config, **kwargs)
        
        # 需求分析相关配置
        self.supported_formats = [
            "user_story", "prd", "api_doc", "feature_spec", 
            "acceptance_criteria", "business_rule"
        ]
        
        # 分析模板
        self.analysis_template = {
            "requirement_summary": "",
            "functional_requirements": [],
            "non_functional_requirements": [],
            "business_rules": [],
            "acceptance_criteria": [],
            "test_scenarios": [],
            "edge_cases": [],
            "assumptions": [],
            "dependencies": [],
            "risks": []
        }
    
    async def _setup_agent(self):
        """初始化需求分析智能体"""
        logger.info(f"🔧 设置需求分析智能体: {self.name}")
        
        # 加载需求分析知识库
        self.knowledge_base = {
            "requirement_patterns": [
                r"作为.*我希望.*以便.*",  # 用户故事模式
                r"给定.*当.*那么.*",      # BDD模式
                r"如果.*则.*否则.*",      # 条件模式
            ],
            "test_types": {
                "functional": ["正常流程", "异常流程", "边界条件"],
                "performance": ["响应时间", "并发性能", "资源使用"],
                "security": ["权限验证", "数据安全", "输入验证"],
                "usability": ["用户体验", "界面友好性", "操作便捷性"],
                "compatibility": ["浏览器兼容", "设备兼容", "版本兼容"]
            },
            "risk_keywords": [
                "复杂", "关键", "重要", "敏感", "集成", "第三方", 
                "性能", "安全", "并发", "大数据", "实时"
            ]
        }
        
        logger.info(f"✅ 需求分析智能体 {self.name} 设置完成")
    
    async def _execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """执行需求分析任务"""
        
        task_type = task.get("type", "analyze_requirement")
        requirement_text = task.get("requirement_text", "")
        requirement_format = task.get("format", "text")
        
        logger.info(f"📋 开始分析需求 [格式: {requirement_format}]")
        
        if task_type == "analyze_requirement":
            return await self._analyze_requirement(requirement_text, requirement_format)
        elif task_type == "extract_test_points":
            return await self._extract_test_points(requirement_text)
        elif task_type == "identify_risks":
            return await self._identify_risks(requirement_text)
        else:
            raise ValueError(f"不支持的任务类型: {task_type}")
    
    async def _analyze_requirement(self, requirement_text: str, format_type: str) -> Dict[str, Any]:
        """分析需求文档"""
        
        # 构建分析提示
        analysis_prompt = f"""
        请分析以下{format_type}格式的需求文档，并按照结构化格式输出分析结果：

        需求文档：
        {requirement_text}

        请按照以下JSON格式输出分析结果：
        {{
            "requirement_summary": "需求概述",
            "functional_requirements": [
                {{
                    "id": "FR001",
                    "description": "功能描述",
                    "priority": "高/中/低",
                    "complexity": "简单/中等/复杂"
                }}
            ],
            "non_functional_requirements": [
                {{
                    "type": "性能/安全/可用性等",
                    "description": "非功能需求描述",
                    "criteria": "具体标准"
                }}
            ],
            "business_rules": [
                {{
                    "rule": "业务规则描述",
                    "condition": "触发条件",
                    "action": "执行动作"
                }}
            ],
            "acceptance_criteria": [
                "验收标准1",
                "验收标准2"
            ],
            "test_scenarios": [
                {{
                    "scenario": "测试场景描述",
                    "type": "正向/负向/边界",
                    "priority": "高/中/低"
                }}
            ],
            "edge_cases": [
                "边界情况1",
                "边界情况2"
            ],
            "assumptions": [
                "假设条件1",
                "假设条件2"
            ],
            "dependencies": [
                "依赖项1",
                "依赖项2"
            ],
            "risks": [
                {{
                    "risk": "风险描述",
                    "impact": "高/中/低",
                    "probability": "高/中/低",
                    "mitigation": "缓解措施"
                }}
            ]
        }}

        请确保分析全面、准确、结构化。
        """
        
        # 调用LLM进行分析
        response = await self._call_llm(analysis_prompt)
        
        try:
            # 解析JSON响应
            analysis_result = json.loads(response)
            
            # 验证和补充分析结果
            validated_result = self._validate_analysis_result(analysis_result)
            
            # 添加元数据
            validated_result["metadata"] = {
                "analyzed_at": datetime.now().isoformat(),
                "analyzer": self.name,
                "format": format_type,
                "text_length": len(requirement_text),
                "analysis_version": "1.0"
            }
            
            # 保存到记忆
            self.add_to_memory({
                "type": "requirement_analysis",
                "result": validated_result
            })
            
            logger.info("✅ 需求分析完成")
            return validated_result
            
        except json.JSONDecodeError as e:
            logger.error(f"❌ 解析分析结果失败: {e}")
            # 返回基础分析结果
            return self._create_fallback_analysis(requirement_text, format_type)
    
    async def _extract_test_points(self, requirement_text: str) -> List[Dict[str, Any]]:
        """提取测试点"""
        
        extract_prompt = f"""
        请从以下需求中提取所有可能的测试点：

        需求：
        {requirement_text}

        请按照以下JSON格式输出测试点：
        [
            {{
                "test_point": "测试点描述",
                "category": "功能/性能/安全/兼容性/可用性",
                "priority": "高/中/低",
                "test_type": "正向/负向/边界/异常",
                "input_data": "测试输入数据要求",
                "expected_result": "预期结果",
                "preconditions": "前置条件",
                "test_steps": ["步骤1", "步骤2"]
            }}
        ]
        """
        
        response = await self._call_llm(extract_prompt)
        
        try:
            test_points = json.loads(response)
            logger.info(f"✅ 提取到 {len(test_points)} 个测试点")
            return test_points
        except json.JSONDecodeError:
            logger.error("❌ 解析测试点失败")
            return []
    
    async def _identify_risks(self, requirement_text: str) -> List[Dict[str, Any]]:
        """识别风险"""
        
        # 基于关键词的风险识别
        identified_risks = []
        
        for keyword in self.knowledge_base["risk_keywords"]:
            if keyword in requirement_text.lower():
                identified_risks.append({
                    "keyword": keyword,
                    "context": self._extract_context(requirement_text, keyword),
                    "risk_level": "待评估"
                })
        
        # 使用LLM进行深度风险分析
        risk_prompt = f"""
        请分析以下需求中的潜在风险：

        需求：
        {requirement_text}

        已识别的关键词风险：
        {json.dumps(identified_risks, ensure_ascii=False, indent=2)}

        请按照以下JSON格式输出风险分析：
        [
            {{
                "risk_id": "RISK001",
                "risk_description": "风险描述",
                "risk_category": "技术/业务/时间/资源",
                "impact": "高/中/低",
                "probability": "高/中/低",
                "risk_score": "1-10分",
                "mitigation_strategy": "缓解策略",
                "contingency_plan": "应急计划"
            }}
        ]
        """
        
        response = await self._call_llm(risk_prompt)
        
        try:
            risks = json.loads(response)
            logger.info(f"✅ 识别到 {len(risks)} 个风险")
            return risks
        except json.JSONDecodeError:
            logger.error("❌ 解析风险分析失败")
            return identified_risks
    
    def _validate_analysis_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """验证和补充分析结果"""
        
        validated = self.analysis_template.copy()
        
        # 合并分析结果
        for key, value in result.items():
            if key in validated:
                validated[key] = value
        
        # 确保必要字段存在
        if not validated["functional_requirements"]:
            validated["functional_requirements"] = [
                {
                    "id": "FR001",
                    "description": "基础功能需求",
                    "priority": "中",
                    "complexity": "中等"
                }
            ]
        
        # 添加自动生成的测试场景
        if not validated["test_scenarios"]:
            validated["test_scenarios"] = self._generate_basic_test_scenarios(validated)
        
        return validated
    
    def _generate_basic_test_scenarios(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成基础测试场景"""
        
        scenarios = []
        
        # 基于功能需求生成场景
        for req in analysis.get("functional_requirements", []):
            scenarios.extend([
                {
                    "scenario": f"验证{req['description']}的正常流程",
                    "type": "正向",
                    "priority": req.get("priority", "中")
                },
                {
                    "scenario": f"验证{req['description']}的异常处理",
                    "type": "负向",
                    "priority": "中"
                }
            ])
        
        return scenarios
    
    def _extract_context(self, text: str, keyword: str, context_size: int = 50) -> str:
        """提取关键词上下文"""
        
        index = text.lower().find(keyword.lower())
        if index == -1:
            return ""
        
        start = max(0, index - context_size)
        end = min(len(text), index + len(keyword) + context_size)
        
        return text[start:end].strip()
    
    def _create_fallback_analysis(self, requirement_text: str, format_type: str) -> Dict[str, Any]:
        """创建备用分析结果"""
        
        return {
            "requirement_summary": f"基于{format_type}格式的需求分析",
            "functional_requirements": [
                {
                    "id": "FR001",
                    "description": "从需求文档中提取的功能",
                    "priority": "中",
                    "complexity": "中等"
                }
            ],
            "test_scenarios": [
                {
                    "scenario": "基础功能验证",
                    "type": "正向",
                    "priority": "高"
                }
            ],
            "metadata": {
                "analyzed_at": datetime.now().isoformat(),
                "analyzer": self.name,
                "format": format_type,
                "fallback": True
            }
        }
    
    async def _call_llm(self, prompt: str) -> str:
        """调用LLM"""
        # 这里应该调用实际的LLM API
        # 为了演示，返回模拟响应
        return '{"requirement_summary": "示例需求分析", "functional_requirements": []}'
