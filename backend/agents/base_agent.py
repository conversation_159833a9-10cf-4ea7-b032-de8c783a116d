"""
基础智能体类 - 所有测试智能体的基类
提供通用功能和接口规范
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
from abc import ABC, abstractmethod
import json
import uuid

from autogen import AssistantAgent
from core.config import settings

logger = logging.getLogger(__name__)

class BaseTestAgent(AssistantAgent, ABC):
    """测试智能体基类"""
    
    def __init__(self, 
                 name: str,
                 system_message: str,
                 llm_config: Dict[str, Any],
                 **kwargs):
        
        super().__init__(
            name=name,
            system_message=system_message,
            llm_config=llm_config,
            **kwargs
        )
        
        self.agent_id = str(uuid.uuid4())
        self.agent_type = self.__class__.__name__
        self.created_at = datetime.now()
        self.last_activity = None
        self.is_ready = False
        
        # 智能体状态
        self.status = "initializing"
        self.current_task = None
        self.task_history: List[Dict] = []
        
        # 依赖关系
        self.dependencies: List[str] = []
        self.dependents: List[str] = []
        
        # 性能指标
        self.metrics = {
            "tasks_completed": 0,
            "tasks_failed": 0,
            "average_response_time": 0.0,
            "total_tokens_used": 0
        }
        
        # 记忆和上下文
        self.memory: List[Dict] = []
        self.context: Dict[str, Any] = {}
        
        logger.info(f"🤖 创建智能体: {name} [{self.agent_type}]")
    
    async def initialize(self):
        """初始化智能体"""
        try:
            await self._setup_agent()
            self.is_ready = True
            self.status = "ready"
            logger.info(f"✅ 智能体 {self.name} 初始化完成")
        except Exception as e:
            self.status = "error"
            logger.error(f"❌ 智能体 {self.name} 初始化失败: {e}")
            raise
    
    @abstractmethod
    async def _setup_agent(self):
        """子类实现的具体初始化逻辑"""
        pass
    
    async def process_task(self, 
                          task: Dict[str, Any], 
                          context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """处理任务的主要方法"""
        
        if not self.is_ready:
            raise RuntimeError(f"智能体 {self.name} 未就绪")
        
        task_id = task.get("id", str(uuid.uuid4()))
        start_time = datetime.now()
        
        logger.info(f"🔄 智能体 {self.name} 开始处理任务: {task_id}")
        
        try:
            self.status = "processing"
            self.current_task = task_id
            self.last_activity = start_time
            
            # 更新上下文
            if context:
                self.context.update(context)
            
            # 执行具体任务
            result = await self._execute_task(task)
            
            # 记录成功
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self._update_metrics(True, duration)
            self._add_to_history(task, result, duration, "success")
            
            self.status = "ready"
            self.current_task = None
            
            logger.info(f"✅ 智能体 {self.name} 任务完成: {task_id} (耗时: {duration:.2f}s)")
            
            return {
                "task_id": task_id,
                "agent_id": self.agent_id,
                "agent_name": self.name,
                "status": "success",
                "result": result,
                "duration": duration,
                "timestamp": end_time.isoformat()
            }
            
        except Exception as e:
            # 记录失败
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            self._update_metrics(False, duration)
            self._add_to_history(task, None, duration, "failed", str(e))
            
            self.status = "error"
            self.current_task = None
            
            logger.error(f"❌ 智能体 {self.name} 任务失败: {task_id} - {e}")
            
            return {
                "task_id": task_id,
                "agent_id": self.agent_id,
                "agent_name": self.name,
                "status": "failed",
                "error": str(e),
                "duration": duration,
                "timestamp": end_time.isoformat()
            }
    
    @abstractmethod
    async def _execute_task(self, task: Dict[str, Any]) -> Any:
        """子类实现的具体任务执行逻辑"""
        pass
    
    def set_dependencies(self, dependencies: List[str]):
        """设置依赖关系"""
        self.dependencies = dependencies
        logger.info(f"🔗 智能体 {self.name} 设置依赖: {dependencies}")
    
    def add_dependent(self, dependent: str):
        """添加依赖者"""
        if dependent not in self.dependents:
            self.dependents.append(dependent)
    
    def add_to_memory(self, item: Dict[str, Any]):
        """添加到记忆中"""
        self.memory.append({
            "timestamp": datetime.now().isoformat(),
            "content": item
        })
        
        # 限制记忆大小
        if len(self.memory) > settings.AGENT_MEMORY_SIZE:
            self.memory = self.memory[-settings.AGENT_MEMORY_SIZE:]
    
    def get_memory(self, limit: Optional[int] = None) -> List[Dict]:
        """获取记忆"""
        if limit:
            return self.memory[-limit:]
        return self.memory
    
    def update_context(self, key: str, value: Any):
        """更新上下文"""
        self.context[key] = value
        self.last_activity = datetime.now()
    
    def get_context(self, key: Optional[str] = None) -> Any:
        """获取上下文"""
        if key:
            return self.context.get(key)
        return self.context
    
    def _update_metrics(self, success: bool, duration: float):
        """更新性能指标"""
        if success:
            self.metrics["tasks_completed"] += 1
        else:
            self.metrics["tasks_failed"] += 1
        
        # 更新平均响应时间
        total_tasks = self.metrics["tasks_completed"] + self.metrics["tasks_failed"]
        current_avg = self.metrics["average_response_time"]
        self.metrics["average_response_time"] = (
            (current_avg * (total_tasks - 1) + duration) / total_tasks
        )
    
    def _add_to_history(self, 
                       task: Dict[str, Any], 
                       result: Any, 
                       duration: float, 
                       status: str, 
                       error: Optional[str] = None):
        """添加到任务历史"""
        history_item = {
            "task_id": task.get("id"),
            "task_type": task.get("type"),
            "status": status,
            "duration": duration,
            "timestamp": datetime.now().isoformat(),
            "result_size": len(str(result)) if result else 0
        }
        
        if error:
            history_item["error"] = error
        
        self.task_history.append(history_item)
        
        # 限制历史大小
        if len(self.task_history) > 100:
            self.task_history = self.task_history[-100:]
    
    def get_status(self) -> Dict[str, Any]:
        """获取智能体状态"""
        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "type": self.agent_type,
            "status": self.status,
            "is_ready": self.is_ready,
            "current_task": self.current_task,
            "created_at": self.created_at.isoformat(),
            "last_activity": self.last_activity.isoformat() if self.last_activity else None,
            "dependencies": self.dependencies,
            "dependents": self.dependents,
            "metrics": self.metrics,
            "memory_size": len(self.memory),
            "context_keys": list(self.context.keys()),
            "task_history_count": len(self.task_history)
        }
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        total_tasks = self.metrics["tasks_completed"] + self.metrics["tasks_failed"]
        success_rate = (
            self.metrics["tasks_completed"] / total_tasks * 100 
            if total_tasks > 0 else 0
        )
        
        return {
            **self.metrics,
            "total_tasks": total_tasks,
            "success_rate": round(success_rate, 2)
        }
    
    def get_task_history(self, limit: Optional[int] = None) -> List[Dict]:
        """获取任务历史"""
        if limit:
            return self.task_history[-limit:]
        return self.task_history
    
    async def reset(self):
        """重置智能体状态"""
        logger.info(f"🔄 重置智能体: {self.name}")
        
        self.status = "ready"
        self.current_task = None
        self.context.clear()
        self.memory.clear()
        self.task_history.clear()
        
        # 重置指标
        self.metrics = {
            "tasks_completed": 0,
            "tasks_failed": 0,
            "average_response_time": 0.0,
            "total_tokens_used": 0
        }
        
        self.last_activity = datetime.now()
    
    async def cleanup(self):
        """清理资源"""
        logger.info(f"🧹 清理智能体资源: {self.name}")
        
        self.is_ready = False
        self.status = "shutdown"
        
        # 清理内存
        self.memory.clear()
        self.context.clear()
        self.task_history.clear()
    
    def __str__(self) -> str:
        return f"{self.name} ({self.agent_type}) - {self.status}"
    
    def __repr__(self) -> str:
        return f"<{self.agent_type}(name='{self.name}', status='{self.status}')>"
