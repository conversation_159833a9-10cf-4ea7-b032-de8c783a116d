"""
智能体编排器 - TestGenius AI的核心大脑
基于Microsoft Autogen框架实现多智能体协作
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import json
import uuid

from autogen import AssistantAgent, UserProxyAgent, GroupChat, GroupChatManager
from autogen.agentchat.contrib.retrieve_assistant_agent import RetrieveAssistantAgent
from autogen.agentchat.contrib.retrieve_user_proxy_agent import RetrieveUserProxyAgent

from core.config import settings
from .base_agent import BaseTestAgent
from .requirement_analyst import RequirementAnalystAgent
from .test_strategist import TestStrategistAgent
from .testcase_generator import TestCaseGeneratorAgent
from .code_generator import CodeGeneratorAgent
from .execution_orchestrator import ExecutionOrchestratorAgent
from .defect_analyst import DefectAnalystAgent
from .report_generator import ReportGeneratorAgent

logger = logging.getLogger(__name__)

class AgentOrchestrator:
    """智能体编排器 - 管理和协调所有测试智能体"""
    
    def __init__(self):
        self.agents: Dict[str, BaseTestAgent] = {}
        self.group_chat: Optional[GroupChat] = None
        self.group_chat_manager: Optional[GroupChatManager] = None
        self.is_ready = False
        self.session_history: Dict[str, List[Dict]] = {}
        
        # 智能体配置
        self.llm_config = settings.get_openai_config()
        
    async def initialize(self):
        """初始化所有智能体"""
        try:
            logger.info("🤖 初始化智能体编排器...")
            
            # 创建各类智能体
            await self._create_agents()
            
            # 设置智能体间的协作关系
            await self._setup_agent_collaboration()
            
            # 创建群聊管理器
            await self._setup_group_chat()
            
            self.is_ready = True
            logger.info("✅ 智能体编排器初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 智能体编排器初始化失败: {e}")
            raise
    
    async def _create_agents(self):
        """创建所有智能体实例"""
        
        # 1. 需求理解智能体
        self.agents["requirement_analyst"] = RequirementAnalystAgent(
            name="需求分析师",
            system_message="""你是一位资深的需求分析师，专门负责：
            1. 解析用户输入的需求文档、用户故事、API接口文档
            2. 提取关键的测试点和业务逻辑
            3. 识别潜在的边界条件和异常场景
            4. 输出结构化的需求分析报告
            
            请始终保持专业、准确、全面的分析态度。""",
            llm_config=self.llm_config
        )
        
        # 2. 测试策略智能体
        self.agents["test_strategist"] = TestStrategistAgent(
            name="测试策略师",
            system_message="""你是一位测试策略专家，负责：
            1. 基于需求分析结果制定测试策略
            2. 确定测试类型和优先级（功能、性能、安全、兼容性）
            3. 规划测试环境和资源分配
            4. 评估测试风险和时间估算
            
            你的策略应该全面、可执行、风险可控。""",
            llm_config=self.llm_config
        )
        
        # 3. 测试用例生成智能体
        self.agents["testcase_generator"] = TestCaseGeneratorAgent(
            name="测试用例生成器",
            system_message="""你是一位测试用例设计专家，擅长：
            1. 基于需求和策略生成高质量测试用例
            2. 应用等价类划分、边界值分析、场景法等技术
            3. 生成正向、负向、边界、异常等各类测试用例
            4. 确保测试用例的完整性和可执行性
            
            生成的测试用例应该结构清晰、步骤明确、预期结果准确。""",
            llm_config=self.llm_config
        )
        
        # 4. 代码生成智能体
        self.agents["code_generator"] = CodeGeneratorAgent(
            name="代码生成器",
            system_message="""你是一位自动化测试代码专家，专门：
            1. 将测试用例转换为可执行的自动化脚本
            2. 支持多种测试框架（Selenium、Playwright、Pytest等）
            3. 生成健壮、可维护的测试代码
            4. 包含适当的等待、断言、异常处理
            
            代码应该遵循最佳实践，具有良好的可读性和可维护性。""",
            llm_config=self.llm_config
        )
        
        # 5. 执行调度智能体
        self.agents["execution_orchestrator"] = ExecutionOrchestratorAgent(
            name="执行调度器",
            system_message="""你是一位测试执行专家，负责：
            1. 协调和调度测试脚本的执行
            2. 管理测试环境和资源分配
            3. 监控测试执行状态和进度
            4. 处理执行过程中的异常和重试
            
            确保测试执行高效、稳定、可追踪。""",
            llm_config=self.llm_config
        )
        
        # 6. 缺陷分析智能体
        self.agents["defect_analyst"] = DefectAnalystAgent(
            name="缺陷分析师",
            system_message="""你是一位缺陷分析专家，专门：
            1. 分析测试失败的日志和错误信息
            2. 定位问题的根本原因
            3. 分类缺陷类型和严重程度
            4. 提供修复建议和预防措施
            
            分析应该深入、准确、具有指导性。""",
            llm_config=self.llm_config
        )
        
        # 7. 报告生成智能体
        self.agents["report_generator"] = ReportGeneratorAgent(
            name="报告生成器",
            system_message="""你是一位测试报告专家，负责：
            1. 汇总所有测试数据和结果
            2. 生成全面、直观的测试报告
            3. 提供数据分析和趋势洞察
            4. 输出多种格式的报告（HTML、PDF、JSON）
            
            报告应该专业、清晰、具有决策价值。""",
            llm_config=self.llm_config
        )
        
        logger.info(f"✅ 已创建 {len(self.agents)} 个智能体")
    
    async def _setup_agent_collaboration(self):
        """设置智能体间的协作关系"""
        
        # 设置智能体间的依赖关系
        dependencies = {
            "requirement_analyst": [],
            "test_strategist": ["requirement_analyst"],
            "testcase_generator": ["requirement_analyst", "test_strategist"],
            "code_generator": ["testcase_generator"],
            "execution_orchestrator": ["code_generator"],
            "defect_analyst": ["execution_orchestrator"],
            "report_generator": ["defect_analyst", "execution_orchestrator"]
        }
        
        for agent_name, deps in dependencies.items():
            if agent_name in self.agents:
                self.agents[agent_name].set_dependencies(deps)
        
        logger.info("✅ 智能体协作关系设置完成")
    
    async def _setup_group_chat(self):
        """设置群聊管理器"""
        
        # 创建智能体列表
        agent_list = list(self.agents.values())
        
        # 创建群聊
        self.group_chat = GroupChat(
            agents=agent_list,
            messages=[],
            max_round=settings.AUTOGEN_MAX_ROUND,
            speaker_selection_method="auto"
        )
        
        # 创建群聊管理器
        self.group_chat_manager = GroupChatManager(
            groupchat=self.group_chat,
            llm_config=self.llm_config,
            system_message="""你是TestGenius AI的智能体协调员，负责：
            1. 协调各个专业智能体的工作流程
            2. 确保信息在智能体间正确传递
            3. 监控任务执行进度和质量
            4. 处理异常情况和冲突解决
            
            请确保整个测试流程高效、准确、完整。"""
        )
        
        logger.info("✅ 群聊管理器设置完成")
    
    async def execute_testing_workflow(self, 
                                     user_input: str, 
                                     session_id: Optional[str] = None) -> Dict[str, Any]:
        """执行完整的测试工作流"""
        
        if not self.is_ready:
            raise RuntimeError("智能体编排器未初始化")
        
        if not session_id:
            session_id = str(uuid.uuid4())
        
        logger.info(f"🚀 开始执行测试工作流 [Session: {session_id}]")
        
        try:
            # 初始化会话历史
            self.session_history[session_id] = []
            
            # 创建用户代理
            user_proxy = UserProxyAgent(
                name="用户",
                system_message="你代表用户提出测试需求。",
                code_execution_config=False,
                human_input_mode="NEVER"
            )
            
            # 开始群聊
            result = await user_proxy.a_initiate_chat(
                self.group_chat_manager,
                message=f"""
                请帮我完成以下测试任务：
                
                {user_input}
                
                请按照以下流程执行：
                1. 需求分析师：分析需求，提取测试点
                2. 测试策略师：制定测试策略和计划
                3. 测试用例生成器：生成详细的测试用例
                4. 代码生成器：生成自动化测试脚本
                5. 执行调度器：执行测试并收集结果
                6. 缺陷分析师：分析失败用例，定位问题
                7. 报告生成器：生成最终测试报告
                
                请确保每个步骤的输出都清晰、完整、可操作。
                """,
                max_turns=settings.AUTOGEN_MAX_ROUND
            )
            
            # 保存会话历史
            self.session_history[session_id] = self.group_chat.messages
            
            # 提取最终结果
            final_result = self._extract_workflow_result(session_id)
            
            logger.info(f"✅ 测试工作流执行完成 [Session: {session_id}]")
            
            return {
                "session_id": session_id,
                "status": "completed",
                "timestamp": datetime.now().isoformat(),
                "result": final_result,
                "message_count": len(self.group_chat.messages)
            }
            
        except Exception as e:
            logger.error(f"❌ 测试工作流执行失败 [Session: {session_id}]: {e}")
            return {
                "session_id": session_id,
                "status": "failed",
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "message_count": len(self.group_chat.messages) if self.group_chat else 0
            }
    
    def _extract_workflow_result(self, session_id: str) -> Dict[str, Any]:
        """从会话历史中提取工作流结果"""
        
        messages = self.session_history.get(session_id, [])
        
        result = {
            "requirement_analysis": None,
            "test_strategy": None,
            "test_cases": None,
            "test_code": None,
            "execution_results": None,
            "defect_analysis": None,
            "final_report": None
        }
        
        # 解析消息，提取各阶段结果
        for message in messages:
            sender = message.get("name", "")
            content = message.get("content", "")
            
            if "需求分析师" in sender:
                result["requirement_analysis"] = content
            elif "测试策略师" in sender:
                result["test_strategy"] = content
            elif "测试用例生成器" in sender:
                result["test_cases"] = content
            elif "代码生成器" in sender:
                result["test_code"] = content
            elif "执行调度器" in sender:
                result["execution_results"] = content
            elif "缺陷分析师" in sender:
                result["defect_analysis"] = content
            elif "报告生成器" in sender:
                result["final_report"] = content
        
        return result
    
    async def get_status(self) -> Dict[str, Any]:
        """获取智能体编排器状态"""
        
        agent_status = {}
        for name, agent in self.agents.items():
            agent_status[name] = {
                "name": agent.name,
                "type": agent.__class__.__name__,
                "ready": hasattr(agent, 'is_ready') and agent.is_ready,
                "last_activity": getattr(agent, 'last_activity', None)
            }
        
        return {
            "orchestrator_ready": self.is_ready,
            "total_agents": len(self.agents),
            "active_sessions": len(self.session_history),
            "agents": agent_status,
            "group_chat_ready": self.group_chat is not None,
            "timestamp": datetime.now().isoformat()
        }
    
    async def get_session_history(self, session_id: str) -> List[Dict]:
        """获取会话历史"""
        return self.session_history.get(session_id, [])
    
    async def cleanup(self):
        """清理资源"""
        logger.info("🔄 清理智能体编排器资源...")
        
        # 清理智能体
        for agent in self.agents.values():
            if hasattr(agent, 'cleanup'):
                await agent.cleanup()
        
        # 清理会话历史
        self.session_history.clear()
        
        self.is_ready = False
        logger.info("✅ 智能体编排器资源清理完成")
