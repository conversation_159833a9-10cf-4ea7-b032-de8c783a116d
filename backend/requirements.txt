# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 数据库
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9
redis==5.0.1

# 异步任务
celery==5.3.4
flower==2.0.1

# AI和机器学习
pyautogen==0.2.16
openai==1.3.7
azure-openai==1.3.0
langchain==0.0.350
langchain-openai==0.0.2
transformers==4.36.2
torch==2.1.2
numpy==1.24.4
pandas==2.1.4
scikit-learn==1.3.2

# 测试框架集成
selenium==4.16.0
playwright==1.40.0
pytest==7.4.3
pytest-asyncio==0.21.1
requests==2.31.0
httpx==0.25.2

# 文档处理
python-docx==1.1.0
PyPDF2==3.0.1
openpyxl==3.1.2
python-multipart==0.0.6

# 图像处理
Pillow==10.1.0
opencv-python==********

# 监控和日志
prometheus-client==0.19.0
structlog==23.2.0
loguru==0.7.2

# 安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# 工具库
python-dotenv==1.0.0
click==8.1.7
rich==13.7.0
typer==0.9.0
jinja2==3.1.2
aiofiles==23.2.1
websockets==12.0

# 数据验证和序列化
marshmallow==3.20.1
marshmallow-sqlalchemy==0.29.0

# HTTP客户端
aiohttp==3.9.1
httpx==0.25.2

# 配置管理
dynaconf==3.2.4

# 任务调度
apscheduler==3.10.4

# 代码质量
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
factory-boy==3.3.0
faker==20.1.0

# 部署
gunicorn==21.2.0
docker==6.1.3

# 云服务
boto3==1.34.0
azure-storage-blob==12.19.0
google-cloud-storage==2.10.0

# 消息队列
pika==1.3.2
kafka-python==2.0.2

# 缓存
python-memcached==1.62
pymemcache==4.0.0

# 时间处理
python-dateutil==2.8.2
pytz==2023.3

# 加密
cryptography==41.0.8
bcrypt==4.1.2

# 网络
dnspython==2.4.2
idna==3.6

# 数据分析
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# 文件处理
chardet==5.2.0
python-magic==0.4.27

# API文档
fastapi-users==12.1.2
fastapi-pagination==0.12.13

# WebSocket
python-socketio==5.10.0

# 邮件
fastapi-mail==1.4.1

# 模板引擎
jinja2==3.1.2

# 国际化
babel==2.13.1

# 性能分析
py-spy==0.3.14
memory-profiler==0.61.0

# 开发工具
ipython==8.18.1
jupyter==1.0.0
