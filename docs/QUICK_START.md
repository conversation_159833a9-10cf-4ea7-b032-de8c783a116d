# 🚀 TestGenius AI - 快速开始指南

## 概述

TestGenius AI 是一个基于 Microsoft Autogen 框架的智能测试平台，通过AI驱动的多智能体协作，实现智能、高效、可扩展的自动化测试流程。

## 系统要求

### 最低要求
- **操作系统**: Linux, macOS, Windows 10+
- **Python**: 3.9+
- **Node.js**: 18+
- **内存**: 8GB RAM
- **存储**: 20GB 可用空间

### 推荐配置
- **操作系统**: Ubuntu 20.04+ / macOS 12+ / Windows 11
- **Python**: 3.11+
- **Node.js**: 20+
- **内存**: 16GB RAM
- **存储**: 50GB SSD
- **Docker**: 最新版本

## 快速安装

### 方式一：一键安装脚本（推荐）

```bash
# 克隆项目
git clone https://github.com/your-org/testgenius-ai.git
cd testgenius-ai

# 运行安装脚本
chmod +x scripts/setup.sh
./scripts/setup.sh
```

安装脚本将自动完成：
- ✅ 检查系统要求
- ✅ 创建环境配置文件
- ✅ 安装项目依赖
- ✅ 初始化数据库
- ✅ 启动开发服务器

### 方式二：手动安装

#### 1. 环境准备

```bash
# 创建Python虚拟环境
cd backend
python3 -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 安装Python依赖
pip install -r requirements.txt

# 安装Node.js依赖
cd ../frontend
npm install
```

#### 2. 配置环境变量

创建 `backend/.env` 文件：
```env
# 基础配置
DEBUG=true
SECRET_KEY=your-secret-key-change-in-production

# 数据库配置
DATABASE_URL=postgresql://testgenius:testgenius123@localhost:5432/testgenius
REDIS_URL=redis://localhost:6379/0

# AI模型配置 (必须配置)
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4-turbo-preview

# 可选：Azure OpenAI配置
# AZURE_OPENAI_API_KEY=your-azure-openai-key
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
```

创建 `frontend/.env` 文件：
```env
VITE_API_URL=http://localhost:8000/api/v1
VITE_WS_URL=ws://localhost:8000/ws
```

#### 3. 启动数据库服务

使用Docker（推荐）：
```bash
docker-compose up -d postgres redis
```

或手动安装PostgreSQL和Redis。

#### 4. 启动应用服务

```bash
# 启动后端服务
cd backend
source venv/bin/activate
uvicorn main:app --host 0.0.0.0 --port 8000 --reload

# 新终端启动前端服务
cd frontend
npm run dev
```

## 访问应用

安装完成后，您可以通过以下地址访问：

- 🌐 **前端应用**: http://localhost:3000
- 🔧 **后端API**: http://localhost:8000
- 📚 **API文档**: http://localhost:8000/docs
- 📊 **监控面板**: http://localhost:3001 (Grafana)
- 🔍 **日志查看**: http://localhost:5601 (Kibana)

## 首次使用

### 1. 创建管理员账户

访问 http://localhost:3000，首次访问会引导您创建管理员账户。

### 2. 配置AI模型

在设置页面配置您的AI模型：
- OpenAI API密钥
- 模型选择（推荐 gpt-4-turbo-preview）
- 温度参数（推荐 0.1）

### 3. 创建第一个项目

1. 点击"项目管理" → "新建项目"
2. 填写项目基本信息
3. 选择测试类型和框架
4. 配置测试环境

### 4. 生成测试用例

1. 在项目中点击"智能生成"
2. 输入需求描述或上传需求文档
3. AI将自动分析并生成测试用例
4. 审核并调整生成的测试用例

## 核心功能演示

### 智能测试用例生成

```python
# 示例：通过API生成测试用例
import requests

response = requests.post('http://localhost:8000/api/v1/testcases/generate', {
    "requirement_text": "用户登录功能：用户输入用户名和密码，点击登录按钮，系统验证用户信息",
    "test_types": ["functional", "security", "boundary"]
})

test_cases = response.json()['data']
```

### 自动化测试执行

```python
# 示例：执行测试套件
response = requests.post('http://localhost:8000/api/v1/execution/run', {
    "project_id": "project_123",
    "test_suite_id": "suite_456",
    "environment": "staging"
})

execution_id = response.json()['data']['execution_id']
```

### 实时监控

前端提供实时仪表盘，显示：
- 测试执行进度
- 成功/失败统计
- 性能指标
- 缺陷分布

## 常见问题

### Q: 如何配置OpenAI API密钥？
A: 在 `backend/.env` 文件中设置 `OPENAI_API_KEY=your-api-key`，或在Web界面的设置页面配置。

### Q: 支持哪些测试框架？
A: 目前支持 Selenium、Playwright、Pytest、JUnit 等主流测试框架。

### Q: 如何添加自定义智能体？
A: 继承 `BaseTestAgent` 类，实现自定义逻辑，然后在编排器中注册。

### Q: 数据库连接失败怎么办？
A: 检查PostgreSQL服务是否启动，确认连接字符串配置正确。

### Q: 前端页面无法加载？
A: 确认后端服务已启动，检查CORS配置和API地址。

## 进阶配置

### 集群部署

使用Kubernetes部署：
```bash
kubectl apply -f k8s/
```

### 监控配置

启用Prometheus监控：
```yaml
# docker-compose.override.yml
services:
  prometheus:
    ports:
      - "9090:9090"
  grafana:
    ports:
      - "3001:3000"
```

### 自定义智能体

```python
# 示例：创建自定义智能体
from agents.base_agent import BaseTestAgent

class CustomTestAgent(BaseTestAgent):
    async def _setup_agent(self):
        # 初始化逻辑
        pass
    
    async def _execute_task(self, task):
        # 任务执行逻辑
        return {"result": "custom_result"}
```

## 获取帮助

- 📖 **文档**: [docs/](./docs/)
- 🐛 **问题反馈**: [GitHub Issues](https://github.com/your-org/testgenius-ai/issues)
- 💬 **社区讨论**: [GitHub Discussions](https://github.com/your-org/testgenius-ai/discussions)
- 📧 **邮件支持**: <EMAIL>

## 下一步

- 📚 阅读 [用户手册](./USER_GUIDE.md)
- 🔧 查看 [开发指南](./DEVELOPMENT.md)
- 🎯 了解 [最佳实践](./BEST_PRACTICES.md)
- 🚀 探索 [高级功能](./ADVANCED_FEATURES.md)

---

**祝您使用愉快！如有任何问题，请随时联系我们。** 🎉
